#!/usr/bin/env python3
"""
Volunteer Comparison Script
Compares volunteers from multiple source Excel files against a master file
and identifies missing volunteers.
"""

import pandas as pd
import numpy as np
from fuzzywuzzy import fuzz, process
import re
from typing import Dict, List, Tuple, Set
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VolunteerComparator:
    def __init__(self):
        # Define column mappings for different files
        self.column_mappings = {
            'name_columns': ['Name', 'Nombre', 'name', 'nombre', 'NOMBRE', 'NAME'],
            'email_columns': ['Email', 'Correo electrónico del asistente', 'Correo', 'email', 'EMAIL', 'correo']
        }
        
        # Source files configuration
        self.source_files = [
            'Sorteo.xlsx',
            'Fomulario 2.xlsx', 
            'Invitados Formulario Inicial.xlsx',
            'Aggregado Manula Multiple Sources.xlsx'
        ]
        
        self.master_file = 'weeze.xlsx'
        self.output_file = 'missing_volunteers.xlsx'
        
        # Fuzzy matching threshold
        self.fuzzy_threshold = 85
        
    def normalize_text(self, text: str) -> str:
        """Normalize text for comparison by removing extra spaces, converting to lowercase, etc."""
        if pd.isna(text) or text is None:
            return ""
        
        # Convert to string and normalize
        text = str(text).strip().lower()
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove special characters but keep letters, numbers, spaces, and common punctuation
        text = re.sub(r'[^\w\s@.-]', '', text)
        
        return text
    
    def normalize_email(self, email: str) -> str:
        """Normalize email addresses for comparison."""
        if pd.isna(email) or email is None:
            return ""
        
        email = str(email).strip().lower()
        # Basic email validation pattern
        if '@' not in email:
            return ""
        
        return email
    
    def find_column(self, df: pd.DataFrame, possible_columns: List[str]) -> str:
        """Find the correct column name from a list of possibilities."""
        for col in possible_columns:
            if col in df.columns:
                return col
        
        # If exact match not found, try case-insensitive matching
        df_columns_lower = [c.lower() for c in df.columns]
        for col in possible_columns:
            col_lower = col.lower()
            if col_lower in df_columns_lower:
                return df.columns[df_columns_lower.index(col_lower)]
        
        return None
    
    def read_volunteer_data(self, file_path: str) -> pd.DataFrame:
        """Read volunteer data from Excel file and normalize column names."""
        try:
            logger.info(f"Reading file: {file_path}")
            df = pd.read_excel(file_path)
            
            # Find name and email columns
            name_col = self.find_column(df, self.column_mappings['name_columns'])
            email_col = self.find_column(df, self.column_mappings['email_columns'])
            
            if not name_col:
                logger.warning(f"No name column found in {file_path}. Available columns: {list(df.columns)}")
                return pd.DataFrame(columns=['name', 'email', 'source_file'])
            
            if not email_col:
                logger.warning(f"No email column found in {file_path}. Available columns: {list(df.columns)}")
                return pd.DataFrame(columns=['name', 'email', 'source_file'])
            
            # Create normalized dataframe
            result_df = pd.DataFrame()
            result_df['name'] = df[name_col].apply(self.normalize_text)
            result_df['email'] = df[email_col].apply(self.normalize_email)
            result_df['source_file'] = file_path
            result_df['original_name'] = df[name_col]
            result_df['original_email'] = df[email_col]
            
            # Remove rows with empty names or emails
            result_df = result_df[
                (result_df['name'] != '') & 
                (result_df['email'] != '') &
                (result_df['email'].str.contains('@', na=False))
            ].copy()
            
            logger.info(f"Successfully read {len(result_df)} valid volunteers from {file_path}")
            return result_df
            
        except Exception as e:
            logger.error(f"Error reading {file_path}: {e}")
            return pd.DataFrame(columns=['name', 'email', 'source_file', 'original_name', 'original_email'])
    
    def fuzzy_match_volunteers(self, source_volunteers: pd.DataFrame, master_volunteers: pd.DataFrame) -> pd.DataFrame:
        """Find volunteers from source that are not in master using fuzzy matching."""
        missing_volunteers = []
        
        # Create sets for exact matching first (faster)
        master_emails = set(master_volunteers['email'].tolist())
        master_names = set(master_volunteers['name'].tolist())
        
        # Create combined name+email strings for fuzzy matching
        master_combined = set()
        for _, row in master_volunteers.iterrows():
            combined = f"{row['name']} {row['email']}"
            master_combined.add(combined)
        
        logger.info(f"Comparing {len(source_volunteers)} source volunteers against {len(master_volunteers)} master volunteers")
        
        for idx, source_row in source_volunteers.iterrows():
            source_name = source_row['name']
            source_email = source_row['email']
            
            # Skip if empty
            if not source_name or not source_email:
                continue
            
            found_match = False
            
            # 1. Exact email match (most reliable)
            if source_email in master_emails:
                found_match = True
            
            # 2. Exact name match
            elif source_name in master_names:
                found_match = True
            
            # 3. Fuzzy matching for names
            else:
                # Try fuzzy matching on names
                best_name_match = process.extractOne(source_name, master_names, scorer=fuzz.ratio)
                if best_name_match and best_name_match[1] >= self.fuzzy_threshold:
                    found_match = True
                
                # Try fuzzy matching on combined name+email
                if not found_match:
                    source_combined = f"{source_name} {source_email}"
                    best_combined_match = process.extractOne(source_combined, master_combined, scorer=fuzz.ratio)
                    if best_combined_match and best_combined_match[1] >= self.fuzzy_threshold:
                        found_match = True
            
            # If no match found, add to missing volunteers
            if not found_match:
                missing_volunteers.append(source_row)
        
        if missing_volunteers:
            missing_df = pd.DataFrame(missing_volunteers)
            logger.info(f"Found {len(missing_df)} missing volunteers")
            return missing_df
        else:
            logger.info("No missing volunteers found")
            return pd.DataFrame(columns=source_volunteers.columns)
    
    def remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove duplicate volunteers based on email and name similarity."""
        if df.empty:
            return df
        
        # First, remove exact duplicates by email
        df_dedup = df.drop_duplicates(subset=['email'], keep='first').copy()
        
        # Then, remove similar names with same email domain
        final_volunteers = []
        processed_emails = set()
        
        for idx, row in df_dedup.iterrows():
            email = row['email']
            name = row['name']
            
            if email in processed_emails:
                continue
            
            # Check for similar names with same email domain
            email_domain = email.split('@')[1] if '@' in email else ''
            is_duplicate = False
            
            for processed_vol in final_volunteers:
                processed_email = processed_vol['email']
                processed_name = processed_vol['name']
                processed_domain = processed_email.split('@')[1] if '@' in processed_email else ''
                
                # If same domain and similar names, consider duplicate
                if (email_domain == processed_domain and 
                    fuzz.ratio(name, processed_name) >= self.fuzzy_threshold):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                final_volunteers.append(row)
                processed_emails.add(email)
        
        result_df = pd.DataFrame(final_volunteers)
        logger.info(f"After deduplication: {len(result_df)} unique missing volunteers")
        return result_df
    
    def generate_summary_stats(self, source_data: Dict[str, pd.DataFrame], 
                             master_data: pd.DataFrame, 
                             missing_volunteers: pd.DataFrame) -> Dict:
        """Generate summary statistics for the comparison."""
        stats = {
            'source_files_stats': {},
            'master_file_stats': {
                'total_volunteers': len(master_data),
                'unique_emails': master_data['email'].nunique()
            },
            'missing_volunteers_stats': {
                'total_missing': len(missing_volunteers),
                'missing_by_source': {}
            }
        }
        
        # Source files statistics
        total_source_volunteers = 0
        for file_name, df in source_data.items():
            stats['source_files_stats'][file_name] = {
                'total_volunteers': len(df),
                'unique_emails': df['email'].nunique()
            }
            total_source_volunteers += len(df)
        
        stats['total_source_volunteers'] = total_source_volunteers
        
        # Missing volunteers by source file
        if not missing_volunteers.empty:
            missing_by_source = missing_volunteers.groupby('source_file').size().to_dict()
            stats['missing_volunteers_stats']['missing_by_source'] = missing_by_source
        
        return stats
    
    def run_comparison(self):
        """Main method to run the volunteer comparison."""
        logger.info("Starting volunteer comparison process...")
        
        # Read all source files
        source_data = {}
        all_source_volunteers = []
        
        for file_path in self.source_files:
            df = self.read_volunteer_data(file_path)
            if not df.empty:
                source_data[file_path] = df
                all_source_volunteers.append(df)
        
        if not all_source_volunteers:
            logger.error("No valid source data found!")
            return
        
        # Combine all source volunteers
        combined_source = pd.concat(all_source_volunteers, ignore_index=True)
        logger.info(f"Total source volunteers: {len(combined_source)}")
        
        # Read master file
        master_data = self.read_volunteer_data(self.master_file)
        if master_data.empty:
            logger.error("No valid master data found!")
            return
        
        # Find missing volunteers
        missing_volunteers = self.fuzzy_match_volunteers(combined_source, master_data)
        
        # Remove duplicates
        missing_volunteers_unique = self.remove_duplicates(missing_volunteers)
        
        # Generate summary statistics
        stats = self.generate_summary_stats(source_data, master_data, missing_volunteers_unique)
        
        # Export results
        self.export_results(missing_volunteers_unique, stats)
        
        return missing_volunteers_unique, stats
    
    def export_results(self, missing_volunteers: pd.DataFrame, stats: Dict):
        """Export missing volunteers and summary to Excel file."""
        try:
            with pd.ExcelWriter(self.output_file, engine='openpyxl') as writer:
                # Export missing volunteers
                if not missing_volunteers.empty:
                    export_df = missing_volunteers[['original_name', 'original_email', 'source_file']].copy()
                    export_df.columns = ['Name', 'Email', 'Source File']
                    export_df.to_excel(writer, sheet_name='Missing Volunteers', index=False)
                    logger.info(f"Exported {len(export_df)} missing volunteers")
                else:
                    # Create empty sheet with headers
                    empty_df = pd.DataFrame(columns=['Name', 'Email', 'Source File'])
                    empty_df.to_excel(writer, sheet_name='Missing Volunteers', index=False)
                    logger.info("No missing volunteers found - created empty sheet")
                
                # Export summary statistics
                summary_data = []
                
                # Source files summary
                summary_data.append(['=== SOURCE FILES SUMMARY ===', '', ''])
                for file_name, file_stats in stats['source_files_stats'].items():
                    summary_data.append([file_name, 'Total Volunteers', file_stats['total_volunteers']])
                    summary_data.append([file_name, 'Unique Emails', file_stats['unique_emails']])
                    summary_data.append(['', '', ''])
                
                # Master file summary
                summary_data.append(['=== MASTER FILE SUMMARY ===', '', ''])
                summary_data.append([self.master_file, 'Total Volunteers', stats['master_file_stats']['total_volunteers']])
                summary_data.append([self.master_file, 'Unique Emails', stats['master_file_stats']['unique_emails']])
                summary_data.append(['', '', ''])
                
                # Missing volunteers summary
                summary_data.append(['=== MISSING VOLUNTEERS SUMMARY ===', '', ''])
                summary_data.append(['Total Missing', '', stats['missing_volunteers_stats']['total_missing']])
                
                if stats['missing_volunteers_stats']['missing_by_source']:
                    summary_data.append(['Missing by Source File:', '', ''])
                    for source_file, count in stats['missing_volunteers_stats']['missing_by_source'].items():
                        summary_data.append([source_file, 'Missing Count', count])
                
                summary_df = pd.DataFrame(summary_data, columns=['Category', 'Metric', 'Value'])
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
                
            logger.info(f"Results exported to {self.output_file}")
            
        except Exception as e:
            logger.error(f"Error exporting results: {e}")

if __name__ == "__main__":
    # Install required packages if not available
    try:
        import fuzzywuzzy
    except ImportError:
        print("Installing required package: fuzzywuzzy")
        import subprocess
        subprocess.check_call(["pip", "install", "fuzzywuzzy", "python-Levenshtein"])
    
    # Run the comparison
    comparator = VolunteerComparator()
    missing_volunteers, stats = comparator.run_comparison()
    
    # Print summary to console
    print("\n" + "="*60)
    print("VOLUNTEER COMPARISON SUMMARY")
    print("="*60)
    
    print(f"\nSource Files Processed:")
    for file_name, file_stats in stats['source_files_stats'].items():
        print(f"  • {file_name}: {file_stats['total_volunteers']} volunteers")
    
    print(f"\nMaster File ({comparator.master_file}): {stats['master_file_stats']['total_volunteers']} volunteers")
    
    print(f"\nMissing Volunteers: {stats['missing_volunteers_stats']['total_missing']}")
    
    if stats['missing_volunteers_stats']['missing_by_source']:
        print("\nMissing by Source File:")
        for source_file, count in stats['missing_volunteers_stats']['missing_by_source'].items():
            print(f"  • {source_file}: {count} missing")
    
    print(f"\nResults exported to: {comparator.output_file}")
    print("="*60)
